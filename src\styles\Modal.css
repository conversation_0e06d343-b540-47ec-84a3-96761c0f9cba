.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.modal-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

/* Modal sizes */
.modal-small {
  width: 100%;
  max-width: 400px;
}

.modal-medium {
  width: 100%;
  max-width: 600px;
}

.modal-large {
  width: 100%;
  max-width: 800px;
}

.modal-extra-large {
  width: 100%;
  max-width: 1000px;
}

.modal-fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* Modal header */
.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  padding: 8px 12px !important;
  font-size: 18px;
  line-height: 1;
  border: none !important;
  background: transparent !important;
  color: #6c757d !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
  transform: none !important;
}

/* Modal body */
.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

/* Modal footer */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-footer.center {
  justify-content: center;
}

.modal-footer.start {
  justify-content: flex-start;
}

.modal-footer.between {
  justify-content: space-between;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-dialog {
    width: 100%;
    max-width: none;
    margin: 0;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 12px 20px;
    flex-direction: column-reverse;
  }
  
  .modal-footer .btn {
    width: 100%;
    margin: 0;
  }
}

/* Modal variants */
.modal-success .modal-header {
  background-color: #d4edda;
  border-bottom-color: #c3e6cb;
}

.modal-success .modal-title {
  color: #155724;
}

.modal-warning .modal-header {
  background-color: #fff3cd;
  border-bottom-color: #ffeaa7;
}

.modal-warning .modal-title {
  color: #856404;
}

.modal-danger .modal-header {
  background-color: #f8d7da;
  border-bottom-color: #f5c6cb;
}

.modal-danger .modal-title {
  color: #721c24;
}

.modal-info .modal-header {
  background-color: #d1ecf1;
  border-bottom-color: #bee5eb;
}

.modal-info .modal-title {
  color: #0c5460;
}

/* Scrollable body */
.modal-body-scrollable {
  max-height: 400px;
  overflow-y: auto;
}

/* No padding body */
.modal-body-no-padding {
  padding: 0;
}

/* Centered content */
.modal-body-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 200px;
}
