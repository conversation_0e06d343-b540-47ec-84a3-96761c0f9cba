// Constants untuk aplikasi

// API URLs
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// App Information
export const APP_INFO = {
  name: 'Frontend App',
  version: '1.0.0',
  description: 'Kerangka frontend modern dengan React dan Vite',
  author: 'Frontend Developer',
  email: '<EMAIL>',
  phone: '+62 ***********'
};

// Navigation Menu
export const NAVIGATION_MENU = [
  { path: '/', label: 'Home', icon: '🏠' },
  { path: '/about', label: 'About', icon: 'ℹ️' },
  { path: '/contact', label: 'Contact', icon: '📞' }
];

// Social Media Links
export const SOCIAL_LINKS = {
  facebook: 'https://facebook.com/frontendapp',
  twitter: 'https://twitter.com/frontendapp',
  instagram: 'https://instagram.com/frontendapp',
  linkedin: 'https://linkedin.com/company/frontendapp',
  github: 'https://github.com/frontendapp'
};

// Theme Colors
export const COLORS = {
  primary: '#007bff',
  secondary: '#6c757d',
  success: '#28a745',
  danger: '#dc3545',
  warning: '#ffc107',
  info: '#17a2b8',
  light: '#f8f9fa',
  dark: '#343a40'
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  mobile: '768px',
  tablet: '992px',
  desktop: '1200px'
};

// Form validation messages
export const VALIDATION_MESSAGES = {
  required: 'Field ini wajib diisi',
  email: 'Format email tidak valid',
  minLength: (min) => `Minimal ${min} karakter`,
  maxLength: (max) => `Maksimal ${max} karakter`,
  phone: 'Format nomor telepon tidak valid'
};

// Local storage keys
export const STORAGE_KEYS = {
  user: 'frontend_app_user',
  theme: 'frontend_app_theme',
  language: 'frontend_app_language',
  settings: 'frontend_app_settings'
};

// Animation durations (in ms)
export const ANIMATION_DURATION = {
  fast: 200,
  normal: 300,
  slow: 500
};

// File upload settings
export const FILE_UPLOAD = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
};

// Pagination settings
export const PAGINATION = {
  defaultPageSize: 10,
  pageSizeOptions: [5, 10, 20, 50]
};

// Date formats
export const DATE_FORMATS = {
  display: 'DD MMMM YYYY',
  input: 'YYYY-MM-DD',
  api: 'YYYY-MM-DD HH:mm:ss'
};

// Error messages
export const ERROR_MESSAGES = {
  network: 'Terjadi kesalahan jaringan. Silakan coba lagi.',
  server: 'Terjadi kesalahan server. Silakan coba lagi nanti.',
  notFound: 'Data tidak ditemukan.',
  unauthorized: 'Anda tidak memiliki akses untuk melakukan tindakan ini.',
  validation: 'Data yang dimasukkan tidak valid.'
};

// Success messages
export const SUCCESS_MESSAGES = {
  save: 'Data berhasil disimpan',
  update: 'Data berhasil diperbarui',
  delete: 'Data berhasil dihapus',
  send: 'Pesan berhasil dikirim'
};
