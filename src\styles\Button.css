.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;
  font-family: inherit;
  line-height: 1;
  position: relative;
  overflow: hidden;
}

/* Button variants */
.btn-primary {
  background-color: rgb(200, 134, 58);
  color: white;
}

.btn-primary:hover:not(.disabled):not(.loading) {
  background-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: #007bff;
  border: 2px solid #007bff;
}

.btn-secondary:hover:not(.disabled):not(.loading) {
  background-color: #007bff;
  color: white;
  transform: translateY(-2px);
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover:not(.disabled):not(.loading) {
  background-color: #1e7e34;
  transform: translateY(-2px);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(.disabled):not(.loading) {
  background-color: #c82333;
  transform: translateY(-2px);
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(.disabled):not(.loading) {
  background-color: #e0a800;
  transform: translateY(-2px);
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover:not(.disabled):not(.loading) {
  background-color: #138496;
  transform: translateY(-2px);
}

.btn-light {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
}

.btn-light:hover:not(.disabled):not(.loading) {
  background-color: #e2e6ea;
  transform: translateY(-2px);
}

.btn-dark {
  background-color: #343a40;
  color: white;
}

.btn-dark:hover:not(.disabled):not(.loading) {
  background-color: #23272b;
  transform: translateY(-2px);
}

/* Button sizes */
.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}

.btn-medium {
  padding: 12px 24px;
  font-size: 16px;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

/* Button states */
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn.loading {
  cursor: not-allowed;
  opacity: 0.8;
}

/* Loading spinner */
.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Full width button */
.btn-block {
  width: 100%;
}

/* Icon buttons */
.btn-icon {
  padding: 12px;
  border-radius: 50%;
  min-width: auto;
}

.btn-icon.btn-small {
  padding: 8px;
}

.btn-icon.btn-large {
  padding: 16px;
}

/* Outline variants */
.btn-outline-primary {
  background-color: transparent;
  color: #007bff;
  border: 2px solid #007bff;
}

.btn-outline-primary:hover:not(.disabled):not(.loading) {
  background-color: #007bff;
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: #6c757d;
  border: 2px solid #6c757d;
}

.btn-outline-secondary:hover:not(.disabled):not(.loading) {
  background-color: #6c757d;
  color: white;
}

/* Focus styles */
.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Active state */
.btn:active:not(.disabled):not(.loading) {
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
  .btn-large {
    padding: 14px 28px;
    font-size: 16px;
  }

  .btn-medium {
    padding: 10px 20px;
    font-size: 14px;
  }
}
