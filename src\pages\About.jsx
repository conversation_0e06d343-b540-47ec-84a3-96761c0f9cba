const About = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Tentang{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              Frontend App
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto">
            Kerangka kerja frontend modern yang dirancang untuk memberikan
            pengalaman development terbaik
          </p>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {/* What is Frontend App */}
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
                Apa itu Frontend App?
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                Frontend App adalah kerangka kerja frontend modern yang dibangun
                menggunakan teknologi terdepan seperti React, Vite, dan Tailwind
                CSS. Aplikasi ini dirancang untuk memberikan pengalaman
                development yang cepat, efisien, dan menyenangkan bagi para
                developer.
              </p>
            </div>

            {/* Technologies Used */}
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">
                Teknologi yang Digunakan
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">⚛️</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">
                      React 19
                    </h4>
                  </div>
                  <p className="text-gray-700">
                    Library JavaScript modern untuk membangun user interface
                    yang interaktif dan responsif
                  </p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">⚡</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">Vite</h4>
                  </div>
                  <p className="text-gray-700">
                    Build tool yang sangat cepat untuk development dengan hot
                    reload dan optimasi build
                  </p>
                </div>

                <div className="bg-gradient-to-br from-cyan-50 to-cyan-100 rounded-2xl p-6 border border-cyan-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-cyan-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">🎨</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">
                      Tailwind CSS
                    </h4>
                  </div>
                  <p className="text-gray-700">
                    Utility-first CSS framework untuk styling yang cepat dan
                    konsisten
                  </p>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">🧪</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">Vitest</h4>
                  </div>
                  <p className="text-gray-700">
                    Testing framework yang cepat dan modern untuk memastikan
                    kualitas kode
                  </p>
                </div>

                <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-orange-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">🔀</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">
                      React Router
                    </h4>
                  </div>
                  <p className="text-gray-700">
                    Routing library untuk navigasi dan pengelolaan halaman dalam
                    SPA
                  </p>
                </div>

                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 border border-yellow-200">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-yellow-600 rounded-xl flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">📦</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900">Node.js</h4>
                  </div>
                  <p className="text-gray-700">
                    Runtime JavaScript untuk development tools dan package
                    management
                  </p>
                </div>
              </div>
            </div>

            {/* Key Features */}
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">
                Keunggulan Utama
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Development Cepat
                  </h4>
                  <p className="text-gray-600">
                    Development server yang sangat cepat dengan hot reload
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🔄</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Hot Reload
                  </h4>
                  <p className="text-gray-600">
                    Perubahan real-time tanpa refresh halaman
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📁</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Struktur Terorganisir
                  </h4>
                  <p className="text-gray-600">
                    Folder dan file yang terstruktur dengan baik
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-yellow-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🧩</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Komponen Reusable
                  </h4>
                  <p className="text-gray-600">
                    Komponen yang dapat digunakan kembali
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📱</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Responsive Design
                  </h4>
                  <p className="text-gray-600">
                    Design yang mobile-friendly dan adaptif
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">✅</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Testing Ready
                  </h4>
                  <p className="text-gray-600">
                    Setup testing yang siap pakai dengan Vitest
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-2xl p-8 text-center text-white">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Siap Memulai Project Anda?
              </h3>
              <p className="text-lg mb-6 opacity-90">
                Frontend App memberikan semua yang Anda butuhkan untuk membangun
                aplikasi web modern
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
                  Mulai Sekarang
                </button>
                <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
                  Lihat Dokumentasi
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
