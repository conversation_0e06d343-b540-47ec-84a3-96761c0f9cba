.about {
  padding: 60px 0;
  background-color: #ffffff;
}

.about h1 {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-text h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #34495e;
}

.about-text h3 {
  font-size: 1.5rem;
  margin-top: 40px;
  margin-bottom: 15px;
  color: #2980b9;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 20px;
}

.about-text ul {
  margin: 20px 0;
  padding-left: 20px;
}

.about-text ul li {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 10px;
}

.about-text ul li strong {
  color: #2980b9;
  font-weight: 600;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .about {
    padding: 40px 0;
  }
  
  .about h1 {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }
  
  .about-text h2 {
    font-size: 1.8rem;
  }
  
  .about-text h3 {
    font-size: 1.3rem;
  }
  
  .about-text p,
  .about-text ul li {
    font-size: 1rem;
  }
}
