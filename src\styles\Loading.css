.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

.loading-spinner {
  margin-bottom: 15px;
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Size variations */
.loading.small .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading.medium .spinner {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.loading.large .spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.loading-text {
  color: #666;
  font-size: 14px;
  margin: 0;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Alternative spinner styles */
.loading.dots .spinner {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  position: relative;
}

.loading.dots .spinner::before,
.loading.dots .spinner::after {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #007bff;
  border-radius: 50%;
  animation: dots 1.4s infinite ease-in-out both;
}

.loading.dots .spinner::before {
  left: 0;
  animation-delay: -0.32s;
}

.loading.dots .spinner::after {
  left: 16px;
  animation-delay: -0.16s;
}

@keyframes dots {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
