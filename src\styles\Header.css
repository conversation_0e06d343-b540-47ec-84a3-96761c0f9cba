.header {
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 80px;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.logo h1 {
  margin: 0;
  color: #007bff;
  font-size: 24px;
  font-weight: 700;
}

.logo a {
  text-decoration: none;
}

.navigation ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  gap: 30px;
}

.navigation a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 16px;
  transition: color 0.3s ease;
  position: relative;
}

.navigation a:hover {
  color: #007bff;
}

.navigation a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.navigation a:hover::after {
  width: 100%;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    height: auto;
    padding: 15px 20px;
  }
  
  .navigation ul {
    gap: 20px;
    margin-top: 10px;
  }
  
  .main-content {
    padding-top: 120px;
  }
}
