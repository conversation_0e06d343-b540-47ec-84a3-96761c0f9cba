.contact {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.contact h1 {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-info h2,
.contact-form h2 {
  font-size: 2rem;
  margin-bottom: 30px;
  color: #34495e;
}

.contact-item {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.contact-item h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: #2980b9;
}

.contact-item p {
  font-size: 1.1rem;
  color: #555;
  margin: 0;
}

.contact-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #34495e;
  font-size: 1rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2980b9;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .contact {
    padding: 40px 0;
  }
  
  .contact h1 {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .contact-form {
    padding: 30px 20px;
  }
}
