import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Lo<PERSON>, Modal } from "../components";

const Demo = () => {
  const [showModal, setShowModal] = useState(false);
  const [showAlert, setShowAlert] = useState(true);

  return (
    <div className="bg-gray-50 min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Component Demo
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Showcase semua komponen yang tersedia dengan styling Tailwind CSS
          </p>
        </div>

        <div className="space-y-16">
          {/* Buttons Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Buttons</h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Variants
                  </h3>
                  <div className="flex flex-wrap gap-4">
                    <Button variant="primary">Primary</Button>
                    <Button variant="secondary">Secondary</Button>
                    <Button variant="success">Success</Button>
                    <Button variant="danger">Danger</Button>
                    <Button variant="warning">Warning</Button>
                    <Button variant="info">Info</Button>
                    <Button variant="light">Light</Button>
                    <Button variant="dark">Dark</Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Sizes
                  </h3>
                  <div className="flex flex-wrap items-center gap-4">
                    <Button size="small">Small</Button>
                    <Button size="medium">Medium</Button>
                    <Button size="large">Large</Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    States
                  </h3>
                  <div className="flex flex-wrap gap-4">
                    <Button>Normal</Button>
                    <Button disabled>Disabled</Button>
                    <Button loading>Loading</Button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Alerts Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Alerts</h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="space-y-4">
                <Alert variant="success">
                  <strong>Success!</strong> Your action was completed
                  successfully.
                </Alert>
                <Alert variant="error">
                  <strong>Error!</strong> Something went wrong. Please try
                  again.
                </Alert>
                <Alert variant="warning">
                  <strong>Warning!</strong> Please check your input before
                  proceeding.
                </Alert>
                <Alert variant="info">
                  <strong>Info!</strong> Here's some helpful information for
                  you.
                </Alert>
                {showAlert && (
                  <Alert
                    variant="success"
                    dismissible
                    onDismiss={() => setShowAlert(false)}
                  >
                    <strong>Dismissible Alert!</strong> You can close this alert
                    by clicking the X button.
                  </Alert>
                )}
              </div>
            </div>
          </section>

          {/* Badges Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Badges</h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Variants
                  </h3>
                  <div className="flex flex-wrap gap-4">
                    <Badge variant="primary">Primary</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="success">Success</Badge>
                    <Badge variant="danger">Danger</Badge>
                    <Badge variant="warning">Warning</Badge>
                    <Badge variant="info">Info</Badge>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Sizes
                  </h3>
                  <div className="flex flex-wrap items-center gap-4">
                    <Badge size="small">Small</Badge>
                    <Badge size="medium">Medium</Badge>
                    <Badge size="large">Large</Badge>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Cards Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Cards</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Simple Card
                </h3>
                <p className="text-gray-600 mb-4">
                  This is a simple card component built with Tailwind CSS
                  utility classes.
                </p>
                <Button size="small">Learn More</Button>
              </div>

              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg hover:shadow-xl transition-shadow duration-300">
                <h3 className="text-xl font-bold mb-3">Gradient Card</h3>
                <p className="text-blue-100 mb-4">
                  A beautiful gradient card with custom styling and hover
                  effects.
                </p>
                <Button variant="light" size="small">
                  Get Started
                </Button>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Feature Card
                </h3>
                <p className="text-gray-600 mb-4">
                  Card with icon and hover animation effects for better user
                  experience.
                </p>
                <div className="flex items-center justify-between">
                  <Badge variant="success">New</Badge>
                  <Button variant="primary" size="small">
                    Try Now
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Modal Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Modal</h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <Button onClick={() => setShowModal(true)}>Open Modal</Button>

              <Modal
                isOpen={showModal}
                onClose={() => setShowModal(false)}
                title="Demo Modal"
                footer={
                  <div className="flex gap-3">
                    <Button
                      variant="secondary"
                      onClick={() => setShowModal(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="primary"
                      onClick={() => setShowModal(false)}
                    >
                      Save Changes
                    </Button>
                  </div>
                }
              >
                <p className="text-gray-600 mb-4">
                  This is a demo modal component built with React and styled
                  with Tailwind CSS. It includes proper accessibility features
                  and smooth animations.
                </p>
                <Alert variant="info">
                  <strong>Tip:</strong> You can close this modal by pressing the
                  Escape key or clicking outside.
                </Alert>
              </Modal>
            </div>
          </section>

          {/* Loading Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Loading</h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Small
                  </h3>
                  <Loading size="small" text="Loading..." />
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Medium
                  </h3>
                  <Loading size="medium" text="Processing..." />
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">
                    Large
                  </h3>
                  <Loading size="large" text="Please wait..." />
                </div>
              </div>
            </div>
          </section>

          {/* Typography Section */}
          <section>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              Typography
            </h2>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="space-y-6">
                <div>
                  <h1 className="text-4xl font-bold text-gray-900">
                    Heading 1
                  </h1>
                  <h2 className="text-3xl font-bold text-gray-800">
                    Heading 2
                  </h2>
                  <h3 className="text-2xl font-bold text-gray-700">
                    Heading 3
                  </h3>
                  <h4 className="text-xl font-bold text-gray-600">Heading 4</h4>
                </div>
                <div>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    This is a large paragraph with relaxed line height for
                    better readability. Tailwind CSS provides excellent
                    typography utilities.
                  </p>
                  <p className="text-base text-gray-600 mt-4">
                    This is a regular paragraph with normal text size and
                    spacing.
                  </p>
                  <p className="text-sm text-gray-500 mt-4">
                    This is a small paragraph, often used for captions or
                    secondary information.
                  </p>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default Demo;
