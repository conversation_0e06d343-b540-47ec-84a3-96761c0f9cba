.card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.card-shadow {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-hover:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 20px;
}

.card-title {
  margin: 0 0 10px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.card-subtitle {
  margin: 0 0 15px 0;
  font-size: 0.9rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.card-body {
  color: #555;
  line-height: 1.6;
}

.card-body p {
  margin-bottom: 15px;
}

.card-body p:last-child {
  margin-bottom: 0;
}

/* Card variants */
.card-primary {
  border-color: #007bff;
}

.card-primary .card-title {
  color: #007bff;
}

.card-success {
  border-color: #28a745;
}

.card-success .card-title {
  color: #28a745;
}

.card-warning {
  border-color: #ffc107;
}

.card-warning .card-title {
  color: #e0a800;
}

.card-danger {
  border-color: #dc3545;
}

.card-danger .card-title {
  color: #dc3545;
}

/* Card sizes */
.card-small {
  max-width: 250px;
}

.card-medium {
  max-width: 350px;
}

.card-large {
  max-width: 450px;
}

/* Card layouts */
.card-horizontal {
  display: flex;
  flex-direction: row;
}

.card-horizontal .card-image {
  width: 40%;
  height: auto;
  min-height: 150px;
}

.card-horizontal .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Responsive */
@media (max-width: 768px) {
  .card-horizontal {
    flex-direction: column;
  }
  
  .card-horizontal .card-image {
    width: 100%;
    height: 200px;
  }
  
  .card-content {
    padding: 15px;
  }
  
  .card-title {
    font-size: 1.1rem;
  }
}

/* Card with actions */
.card-actions {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.card-actions.center {
  justify-content: center;
}

.card-actions.start {
  justify-content: flex-start;
}

.card-actions.between {
  justify-content: space-between;
}

/* Card with header */
.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.card-header .card-title {
  margin: 0;
  font-size: 1.1rem;
}

.card-header + .card-content {
  padding-top: 20px;
}

/* Loading state */
.card-loading {
  position: relative;
  overflow: hidden;
}

.card-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
